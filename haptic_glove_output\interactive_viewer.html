
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Haptic VR Glove - Interactive 3D Blueprint</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/STLLoader.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Segoe UI', sans-serif;
            overflow: hidden;
        }
        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        #info {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            max-width: 300px;
            z-index: 100;
        }
        #controls {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            z-index: 100;
        }
        .control-group {
            margin: 10px 0;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 2px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #45a049;
        }
        #stats {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px;
            border-radius: 10px;
            z-index: 100;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="info">
            <h2>🔧 Haptic VR Glove</h2>
            <p><strong>Interactive 3D Blueprint</strong></p>
            <p>📐 All components positioned with correct mechanical relationships</p>
            <p>🔍 Use mouse to rotate, zoom, and inspect</p>
            <p>⚙️ Based on OpenSCAD specifications</p>
        </div>

        <div id="controls">
            <h3>🎮 Controls</h3>
            <div class="control-group">
                <button onclick="resetView()">🏠 Reset View</button>
                <button onclick="toggleWireframe()">📐 Wireframe</button>
            </div>
            <div class="control-group">
                <button onclick="showAxes()">📊 Toggle Axes</button>
                <button onclick="toggleGrid()">⊞ Toggle Grid</button>
            </div>
            <div class="control-group">
                <button onclick="explodeView()">💥 Explode View</button>
                <button onclick="animateAssembly()">🔄 Animate</button>
            </div>
        </div>

        <div id="stats">
            <div><strong>Components:</strong> 31</div>
            <div><strong>Motors:</strong> 5 × N20 6V 298:1</div>
            <div><strong>Gears:</strong> 5 × Module 0.5, 10-tooth</div>
            <div><strong>Bearings:</strong> 5 × MR85-2RS</div>
        </div>
    </div>

    <script>
        // Global variables
        let scene, camera, renderer, controls;
        let components = [];
        let wireframeMode = false;
        let exploded = false;
        let axesHelper, gridHelper;

        // Component specifications from your bill of materials
        const MOTOR_LEN = 26.0, MOTOR_W = 10.0, MOTOR_H = 12.0;
        const GEAR_OD = 6.0, GEAR_TH = 2.0;
        const BEARING_OD = 8.0, BEARING_H = 2.5;
        const FINGER_SPACING = 22.0;

        function init() {
            // Create scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x1a1a2e);

            // Create camera
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(50, 50, 50);

            // Create renderer
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            document.getElementById('container').appendChild(renderer.domElement);

            // Add controls
            controls = new THREE.OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;

            // Add lighting
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(50, 50, 50);
            directionalLight.castShadow = true;
            scene.add(directionalLight);

            // Add axes helper
            axesHelper = new THREE.AxesHelper(30);
            scene.add(axesHelper);

            // Add grid helper
            gridHelper = new THREE.GridHelper(100, 20, 0x444444, 0x444444);
            scene.add(gridHelper);

            // Create the haptic glove assembly
            createHapticGloveAssembly();

            // Start animation loop
            animate();
        }

        function createHapticGloveAssembly() {
            // Create 5 finger units
            for (let i = 0; i < 5; i++) {
                createFingerUnit(i);
            }

            // Create back-of-hand PCB
            createPCB();
        }

        function createFingerUnit(fingerIndex) {
            const group = new THREE.Group();
            group.name = `Finger_${fingerIndex + 1}`;

            // Motor (rectangular box)
            const motorGeometry = new THREE.BoxGeometry(MOTOR_LEN, MOTOR_W, MOTOR_H);
            const motorMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
            const motor = new THREE.Mesh(motorGeometry, motorMaterial);
            motor.position.set(0, fingerIndex * FINGER_SPACING, MOTOR_H/2);
            motor.name = `Motor_${fingerIndex + 1}`;
            group.add(motor);

            // Gear (cylinder with teeth)
            const gearGeometry = new THREE.CylinderGeometry(GEAR_OD/2, GEAR_OD/2, GEAR_TH, 16);
            const gearMaterial = new THREE.MeshLambertMaterial({ color: 0xFFD700 });
            const gear = new THREE.Mesh(gearGeometry, gearMaterial);
            gear.position.set(MOTOR_LEN/2 + GEAR_TH/2, fingerIndex * FINGER_SPACING, MOTOR_H/2);
            gear.rotation.x = Math.PI/2;
            gear.name = `Gear_${fingerIndex + 1}`;
            group.add(gear);

            // Bearing (ring)
            const bearingGeometry = new THREE.RingGeometry(2.5, BEARING_OD/2, 16);
            const bearingMaterial = new THREE.MeshLambertMaterial({ color: 0x888888 });
            const bearing = new THREE.Mesh(bearingGeometry, bearingMaterial);
            bearing.position.set(MOTOR_LEN/2 + GEAR_TH/2, fingerIndex * FINGER_SPACING, MOTOR_H/2 + BEARING_H/2);
            bearing.name = `Bearing_${fingerIndex + 1}`;
            group.add(bearing);

            // Output drum
            const drumGeometry = new THREE.CylinderGeometry((GEAR_OD + 4)/2, (GEAR_OD + 4)/2, GEAR_TH + BEARING_H, 16);
            const drumMaterial = new THREE.MeshLambertMaterial({ color: 0x0066CC });
            const drum = new THREE.Mesh(drumGeometry, drumMaterial);
            drum.position.set(MOTOR_LEN/2 + GEAR_TH/2, fingerIndex * FINGER_SPACING, MOTOR_H/2);
            drum.rotation.x = Math.PI/2;
            drum.name = `Drum_${fingerIndex + 1}`;
            group.add(drum);

            // Cable (line)
            const cableGeometry = new THREE.BufferGeometry();
            const cablePoints = [
                new THREE.Vector3(MOTOR_LEN/2 + GEAR_TH/2, fingerIndex * FINGER_SPACING, MOTOR_H/2),
                new THREE.Vector3(MOTOR_LEN/2 + GEAR_TH/2 + 10, fingerIndex * FINGER_SPACING, MOTOR_H/2 + 5),
                new THREE.Vector3(MOTOR_LEN/2 + GEAR_TH/2 + 20, fingerIndex * FINGER_SPACING, MOTOR_H/2 - 10),
                new THREE.Vector3(MOTOR_LEN/2 + GEAR_TH/2 + 30, fingerIndex * FINGER_SPACING, MOTOR_H/2 - 15)
            ];
            cableGeometry.setFromPoints(cablePoints);
            const cableMaterial = new THREE.LineBasicMaterial({ color: 0xFF0000, linewidth: 2 });
            const cable = new THREE.Line(cableGeometry, cableMaterial);
            cable.name = `Cable_${fingerIndex + 1}`;
            group.add(cable);

            scene.add(group);
            components.push(group);
        }

        function createPCB() {
            const pcbGeometry = new THREE.BoxGeometry(50, 30, 1.6);
            const pcbMaterial = new THREE.MeshLambertMaterial({ color: 0x006600 });
            const pcb = new THREE.Mesh(pcbGeometry, pcbMaterial);
            pcb.position.set(MOTOR_LEN + 10, 2 * FINGER_SPACING, 15);
            pcb.name = "PCB_Assembly";

            // ESP32 outline
            const esp32Geometry = new THREE.BoxGeometry(25, 18, 3);
            const esp32Material = new THREE.MeshLambertMaterial({ color: 0x000080 });
            const esp32 = new THREE.Mesh(esp32Geometry, esp32Material);
            esp32.position.set(MOTOR_LEN + 10, 2 * FINGER_SPACING, 15 + 1.6 + 1.5);
            esp32.name = "ESP32";

            const pcbGroup = new THREE.Group();
            pcbGroup.add(pcb);
            pcbGroup.add(esp32);
            pcbGroup.name = "PCB_Assembly";

            scene.add(pcbGroup);
            components.push(pcbGroup);
        }

        function resetView() {
            camera.position.set(50, 50, 50);
            controls.reset();
        }

        function toggleWireframe() {
            wireframeMode = !wireframeMode;
            scene.traverse((child) => {
                if (child.isMesh) {
                    child.material.wireframe = wireframeMode;
                }
            });
        }

        function showAxes() {
            axesHelper.visible = !axesHelper.visible;
        }

        function toggleGrid() {
            gridHelper.visible = !gridHelper.visible;
        }

        function explodeView() {
            exploded = !exploded;
            const offset = exploded ? 30 : 0;

            components.forEach((component, index) => {
                if (component.name.includes('Finger')) {
                    const fingerIndex = parseInt(component.name.split('_')[1]) - 1;
                    component.position.y = fingerIndex * FINGER_SPACING + (exploded ? fingerIndex * offset : 0);
                }
            });
        }

        function animateAssembly() {
            // Simple rotation animation
            components.forEach((component) => {
                component.rotation.z += 0.1;
            });
        }

        function animate() {
            requestAnimationFrame(animate);
            controls.update();
            renderer.render(scene, camera);
        }

        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        window.addEventListener('resize', onWindowResize);

        // Initialize the scene
        init();
    </script>
</body>
</html>
        