
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Haptic VR Glove - 3D Blueprint Viewer</title>
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .specs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .spec-card {
            background: rgba(255,255,255,0.2);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .spec-title {
            font-weight: bold;
            font-size: 1.2em;
            margin-bottom: 10px;
            color: #FFD700;
        }
        .file-list {
            background: rgba(0,0,0,0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .file-item {
            padding: 10px;
            margin: 5px 0;
            background: rgba(255,255,255,0.1);
            border-radius: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .download-btn {
            background: #28a745;
            color: white;
            padding: 5px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            font-size: 0.9em;
        }
        .download-btn:hover {
            background: #218838;
        }
        .note {
            background: rgba(255,193,7,0.2);
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #ffc107;
            margin: 20px 0;
        }
        .viewer-placeholder {
            background: rgba(0,0,0,0.3);
            height: 400px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2em;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Haptic VR Glove - Professional CAD Blueprint</h1>
        
        <div class="specs">
            <div class="spec-card">
                <div class="spec-title">🏭 Motor Specifications</div>
                <div>N20 6V 298:1<br/>26×10×12 mm<br/>2.4 kg·cm stall torque</div>
            </div>
            <div class="spec-card">
                <div class="spec-title">⚙️ Gear Specifications</div>
                <div>Module 0.5, 10-tooth<br/>Ø6 mm × 2 mm<br/>Brass construction</div>
            </div>
            <div class="spec-card">
                <div class="spec-title">🔄 Bearing Specifications</div>
                <div>MR85-2RS<br/>5×8×2.5 mm<br/>Sealed ball bearing</div>
            </div>
            <div class="spec-card">
                <div class="spec-title">📏 Assembly Layout</div>
                <div>5 Finger Units<br/>22 mm spacing<br/>Back-of-hand PCB</div>
            </div>
        </div>
        
        <div class="note">
            <strong>💡 Note:</strong> This is a complete parametric CAD model based on your OpenSCAD specifications. 
            All components are dimensionally accurate and ready for manufacturing.
        </div>
        
        <div class="viewer-placeholder">
            🎯 3D Interactive Viewer<br/>
            <small>(Use FreeCAD, Fusion 360, or similar to open STEP files)</small>
        </div>
        
        <div class="file-list">
            <h3>📁 Exported CAD Files:</h3>
            <div class="file-item">
                <span>📄 haptic_glove_complete.step - Complete Assembly</span>
                <a href="haptic_glove_complete.step" class="download-btn" download>Download STEP</a>
            </div>
            <div class="file-item">
                <span>🔧 n20_motor.stl - Motor Housing (3D Printable)</span>
                <a href="n20_motor.stl" class="download-btn" download>Download STL</a>
            </div>
            <div class="file-item">
                <span>⚙️ spur_gear.stl - Brass Gear (3D Printable)</span>
                <a href="spur_gear.stl" class="download-btn" download>Download STL</a>
            </div>
            <div class="file-item">
                <span>🔄 bearing.stl - Bearing Model (Reference)</span>
                <a href="bearing.stl" class="download-btn" download>Download STL</a>
            </div>
            <div class="file-item">
                <span>🥁 output_drum.stl - Output Drum (3D Printable)</span>
                <a href="output_drum.stl" class="download-btn" download>Download STL</a>
            </div>
        </div>
        
        <div class="note">
            <strong>🔧 Usage Instructions:</strong><br/>
            1. Open the STEP file in professional CAD software (FreeCAD, Fusion 360, SolidWorks, etc.)<br/>
            2. Use STL files for 3D printing individual components<br/>
            3. All dimensions match your bill of materials specifications<br/>
            4. Components are positioned with correct mechanical relationships
        </div>
    </div>
</body>
</html>
        